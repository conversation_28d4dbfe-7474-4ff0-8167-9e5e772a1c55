<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList"
			bgColor="url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/gjbg.png') no-repeat center top/100% 417rpx #fff "
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="房屋估价" mpWeiXinShow :autoBack="true" bgColor="transparent" :fixed="false"
					class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="appraise_container">


					<!-- 白色表单区域 -->
					<view class="form_section">
						<view class="form_title">填写房屋信息</view>

						<!-- 城市选择 -->
						<view class="form_item">
							<view class="form_row">
								<view class="label_text">
									<text class="required_star">*</text>
									<text class="label_name">城市</text>
								</view>
								<view class="input_area" @click="showAddress">
									<view class="input_value" :class="{ inactive: !form.city }">{{ form.city || '请选择' }}
									</view>
									<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
								</view>
							</view>
							<view class="divider_line"></view>
						</view>

						<!-- 小区选择 -->
						<view class="form_item">
							<view class="form_row">
								<view class="label_text">
									<text class="required_star">*</text>
									<text class="label_name">小区</text>
								</view>
								<view class="input_area">
									<input class="input_field" placeholder="请输入小区名称" />
								</view>
							</view>
							<view class="divider_line"></view>
						</view>

						<!-- 房型选择 -->
						<view class="form_item">
							<view class="form_row">
								<view class="label_text">
									<text class="required_star">*</text>
									<text class="label_name">房型</text>
								</view>
								<view class="input_area" @click="selectRoomType">
									<view class="input_value" :class="{ inactive: !form.roomType }">
										{{ form.roomType || '请选择房型' }}
									</view>
									<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
								</view>
							</view>
							<view class="divider_line"></view>
						</view>

						<!-- 房屋面积 -->
						<view class="form_item">
							<view class="form_row">
								<view class="label_text">
									<text class="required_star">*</text>
									<text class="label_name">房屋面积</text>
								</view>
								<view class="input_area">
									<input class="input_field" placeholder="请输入面积" type="number" />
									<view class="unit_text">m²</view>
								</view>
							</view>
							<view class="divider_line"></view>
						</view>

						<!-- 楼层信息 -->
						<view class="form_item" style="margin-top: 30rpx;">
							<view class="label_text">
								<text class="required_star">*</text>
								<text class="label_name">楼层信息</text>
							</view>
							<view class="floor_inputs">
								<view class="floor_input_group">
									<input class="floor_input" placeholder="请输入" type="number" />
									<view class="floor_unit">层</view>
								</view>
								<view class="floor_input_group">
									<input class="floor_input" placeholder="请输入" type="number" />
									<view class="floor_unit">总层数</view>
								</view>
							</view>
							<view class="divider_line"></view>
						</view>

						<!-- 户型朝向 -->
						<view class="form_item">
							<view class="form_row">
								<view class="label_text">
									<text class="required_star">*</text>
									<text class="label_name">户型朝向</text>
								</view>
								<view class="input_area" @click="selectOrientation">
									<view class="input_value" :class="{ inactive: !form.orientation }">
										{{ form.orientation ? form.orientation + '朝向' : '请选择朝向' }}
									</view>
									<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
								</view>
							</view>
							<view class="divider_line"></view>
						</view>

						<!-- 装修类型 -->
						<view class="form_item" style="margin-top: 30rpx;">
							<view class="label_text">
								<text class="required_star">*</text>
								<text class="label_name">装修类型</text>
							</view>
							<view class="option_buttons">
								<view class="option_button" :class="{ active: decorationType === '豪华装' }"
									@click="selectDecoration('豪华装')">豪华装</view>
								<view class="option_button" :class="{ active: decorationType === '精装' }"
									@click="selectDecoration('精装')">精装</view>
								<view class="option_button" :class="{ active: decorationType === '简装' }"
									@click="selectDecoration('简装')">简装</view>
								<view class="option_button" :class="{ active: decorationType === '毛坯' }"
									@click="selectDecoration('毛坯')">毛坯</view>
							</view>
						</view>

						<!-- 是否有电梯 -->
						<view class="form_item">
							<view class="label_text">
								<text class="required_star">*</text>
								<text class="label_name">是否有电梯</text>
							</view>
							<view class="option_buttons">
								<view class="option_button" :class="{ active: hasElevator === true }"
									@click="selectElevator(true)">是</view>
								<view class="option_button" :class="{ active: hasElevator === false }"
									@click="selectElevator(false)">否</view>
							</view>
						</view>

						<!-- 建筑类型 -->
						<view class="form_item">
							<view class="label_text">
								<text class="required_star">*</text>
								<text class="label_name">建筑类型</text>
							</view>
							<view class="option_buttons">
								<view class="option_button" :class="{ active: buildingType === '塔楼' }"
									@click="selectBuilding('塔楼')">塔楼</view>
								<view class="option_button" :class="{ active: buildingType === '板楼' }"
									@click="selectBuilding('板楼')">板楼</view>
								<view class="option_button" :class="{ active: buildingType === '板塔结合' }"
									@click="selectBuilding('板塔结合')">板塔结合</view>
							</view>
						</view>


					</view>
				</view>
			</view>
			<view slot="bottom">
				<!-- 提交按钮 -->
				<view class="bottom">
					<view class="submit_button" @click="submitForm">
						查看评估结果
					</view>
				</view>

			</view>
		</z-paging>
		<addressChoose level="2" ref="addressChoose" @confirm="confirmAddress"></addressChoose>

		<!-- 房型选择器 -->
		<u-picker :show="roomTypeShow" :columns="roomTypeList" :defaultIndex="roomTypeIndex" @confirm="confirmRoomType"
			@cancel="roomTypeShow = false" title="选择房型">
		</u-picker>

		<!-- 朝向选择器 -->
		<u-picker :show="orientationShow" :columns="[orientationList]" :defaultIndex="[orientationIndex]"
			@confirm="confirmOrientation" @cancel="orientationShow = false" title="选择朝向">
		</u-picker>

	</view>
</template>

<script>
import addressChoose from "@/components/common/address_choose.vue";

export default {
	components: {
		addressChoose
	},
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			// 表单数据
			decorationType: '精装', // 装修类型
			hasElevator: true, // 是否有电梯
			buildingType: '板楼', // 建筑类型
			form: {
				province: "",
				city: "",
				roomType: "", // 房型
				orientation: "", // 朝向
			},
			// 房型选择器数据
			roomTypeShow: false,
			roomTypeList: [
				['1室', '2室', '3室', '4室', '5室', '6室'],
				['1厅', '2厅', '3厅', '4厅'],
				['1卫', '2卫', '3卫', '4卫']
			],
			roomTypeIndex: [2, 1, 1], // 默认选中3室2厅2卫
			// 朝向选择器数据
			orientationShow: false,
			orientationList: [
				'东', '南', '西', '北', '东南', '东北', '西南', '西北', '南北'
			],
			orientationIndex: 4 // 默认选中东南
		};
	},
	computed: {},
	onLoad() {
		// 初始化默认房型
		this.form.roomType = this.roomTypeList[0][this.roomTypeIndex[0]] +
			this.roomTypeList[1][this.roomTypeIndex[1]] +
			this.roomTypeList[2][this.roomTypeIndex[2]];

		// 初始化默认朝向
		this.form.orientation = this.orientationList[this.orientationIndex];
	},
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};

			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		confirmAddress(val, lat, lng) {
			this.form.longitude = lng;
			this.form.latitude = lat;
			if (val[0]) this.form.province = val[0];
			if (val[1]) this.form.city = val[1];
			if (val[2]) this.form.area = val[2];
			if (val[3]) this.form.town = val[3];
		},
		showAddress() {
			this.$refs.addressChoose.init();
		},
		// 选择房型
		selectRoomType() {
			this.roomTypeShow = true;
		},
		// 确认房型选择
		confirmRoomType(e) {
			const { indexs, value } = e;
			this.roomTypeIndex = indexs;
			this.form.roomType = value.join('');
			this.roomTypeShow = false;
		},
		// 选择朝向
		selectOrientation() {
			this.orientationShow = true;
		},
		// 确认朝向选择
		confirmOrientation(e) {
			const { indexs, value } = e;
			this.orientationIndex = indexs[0];
			this.form.orientation = value[0];
			this.orientationShow = false;
		},
		// 选择装修类型
		selectDecoration(type) {
			this.decorationType = type;
		},
		// 选择电梯
		selectElevator(hasElevator) {
			this.hasElevator = hasElevator;
		},
		// 选择建筑类型
		selectBuilding(type) {
			this.buildingType = type;
		},
		// 提交表单
		submitForm() {
			console.log('提交表单');
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding-top: 290rpx;

		.appraise_container {
			width: 100%;



			// 白色表单区域
			.form_section {
				background: #ffffff;
				border-radius: 30rpx 30rpx 0 0;
				margin-top: -30rpx;
				position: relative;
				z-index: 2;
				padding: 30rpx 25rpx 0 25rpx;
				box-shadow: 0px 6px 12px 0px rgba(216, 216, 216, 0.1608);

				// 表单标题
				.form_title {
					font-size: 36rpx;
					font-weight: bold;
					color: #000000;
					line-height: 36rpx;
				}




			}
		}

		.label_text {
			@include flex-center(row, flex-start, center);

			.required_star {
				color: #F63030;
				font-size: 28rpx;
				font-weight: bold;
				margin-right: 4rpx;
			}

			.label_name {
				color: #333333;
				font-size: 28rpx;
				font-weight: bold;
			}
		}

		.input_area {
			@include flex-center(row, flex-end, center);
			flex: 1;
			margin-left: 40rpx;

			.input_value {
				color: #333333;
				font-size: 28rpx;
				margin-right: 20rpx;

				&.inactive {
					color: #BFBFBF;
				}
			}

			.input_field {
				flex: 1;
				text-align: right;
				color: #333333;
				font-size: 28rpx;

				&::placeholder {
					color: #BFBFBF;
					font-size: 28rpx;
				}
			}

			.unit_text {
				color: #333333;
				font-size: 28rpx;
				margin-left: 20rpx;
			}
		}

		// 表单项
		.form_item {

			// 表单行
			.form_row {
				@include flex-center(row, space-between, center);
				padding: 30rpx 0;


			}

			// 分割线
			.divider_line {
				height: 1rpx;
				background: #EEEEEE;
			}

			// 楼层输入
			.floor_inputs {
				@include flex-center(row, space-between, center);
				margin: 20rpx 0;

				.floor_input_group {
					@include flex-center(row, flex-start, center);
					flex: 0.7;

					&:last-child {
						justify-content: flex-end;
					}

					.floor_input {
						flex: 0.7;
						color: #333333;
						font-size: 28rpx;

						&::placeholder {
							color: #BFBFBF;
							font-size: 28rpx;
						}
					}

					.floor_unit {
						color: #333333;
						font-size: 28rpx;
						margin-left: 20rpx;
					}
				}

				.floor_separator {
					color: #333333;
					font-size: 28rpx;
					margin: 0 40rpx;
				}
			}

			// 选项按钮组
			.option_buttons {
				@include flex-center(row, flex-start, center, wrap);
				gap: 20rpx;
				margin: 20rpx 0;
				flex-wrap: wrap;

				.option_button {
					padding: 15rpx 30rpx;
					border-radius: 60rpx;
					background: #F8F8F8;
					color: #333333;
					font-size: 26rpx;
					text-align: center;
					min-width: 160rpx;

					&.active {
						background: rgba(0, 106, 252, 0.1);
						color: #006AFC;
					}
				}
			}
		}
	}

	// 提交按钮
	.bottom {
		padding: 20rpx 25rpx;
		background-color: #fff;
		box-shadow: 0rpx -3rpx 6rpx 1rpx rgba(207, 207, 207, 0.27);

		.submit_button {
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}

}
</style>