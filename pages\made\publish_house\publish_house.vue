<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="发布房源" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="form_body">
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>房源标题</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入发布的房源标题" border="none"
								fontSize="28rpx" color="#333" v-model="form.title" inputAlign="right"></u--input>
						</view>
					</view>
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房产证</text>
							<text class="subtitle">可多张</text>
						</view>
						<view class="item_content">
							<view class="item_content_image" v-for="(item, index) in form.picArr">
								<image :src="$t.getImgUrl(item)" mode="">
								</image>
								<view class="item_content_image_icon" @click="form.picArr.splice(index, 1)">
									<u-icon name="close" color="#fff" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/upload_btn.png" mode=""
								@click="chooseImage" v-if="form.picArr.length < 3">
							</image>
						</view>
					</view>
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房屋视频</text>
							<text class="subtitle">选填</text>
						</view>
						<view class="item_content">
							<view class="item_content_image" v-for="(item, index) in form.picArr">
								<image :src="$t.getImgUrl(item)" mode="">
								</image>
								<view class="item_content_image_icon" @click="form.picArr.splice(index, 1)">
									<u-icon name="close" color="#fff" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/upload_btn.png" mode=""
								@click="chooseImage" v-if="form.picArr.length < 3">
							</image>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>城市</text>
						</view>
						<view class="item_content">
							<text :class="{ 'inactive': !form.city }">{{ form.city || '请选择' }}</text>
							<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>小区</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入小区名称" border="none"
								fontSize="28rpx" color="#333" v-model="form.community" inputAlign="right"></u--input>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>房型</text>
						</view>
						<view class="item_content">
							<text :class="{ 'inactive': !form.roomType }">{{ form.roomType || '请选择' }}</text>
							<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>房屋面积</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入面积" border="none"
								fontSize="28rpx" color="#333" v-model="form.area" inputAlign="right"></u--input>
							<view class="unit">m²</view>
						</view>
					</view>
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>楼层信息</text>
						</view>
						<view class="item_content" style="justify-content: space-between;">
							<view class="form_column">
								<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入" border="none"
									fontSize="28rpx" color="#333" v-model="form.area"></u--input>
								<view class="unit">层</view>
							</view>
							<view class="form_column">
								<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入" border="none"
									fontSize="28rpx" color="#333" v-model="form.area"></u--input>
								<view class="unit">总层数</view>
							</view>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>期望售价</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入期望售价" border="none"
								fontSize="28rpx" color="#333" v-model="form.area" inputAlign="right"></u--input>
							<view class="unit">万</view>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>联系方式</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入您的手机号" border="none"
								fontSize="28rpx" color="#333" v-model="form.area" inputAlign="right"></u--input>
							<view class="unit">万</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom" class="bottom">
				<view class="calculate_button">
					确认发布
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			form: {
				picArr: []
			}
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding: 20rpx 0;
		@include flex-center(column, null, null);
		gap: 20rpx;

		.form_body {
			width: 100%;
			padding: 0 25rpx;
			background-color: #fff;



			.form_item {
				width: 100%;
				height: auto;
				@include flex-center(row, space-between, center);
				padding: 30rpx 0;
				border-bottom: 1rpx solid #eee;

				// 表单行
				.form_column {
					width: 33%;
					@include flex-center(row, space-between, center);
				}

				.item_label {
					@include flex-center(row, flex-start, center);
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;

					.required {
						color: #F63030
					}

					.subtitle {
						font-weight: 500;
						font-size: 22rpx;
						color: #BFBFBF;
						margin-left: 15rpx;
					}
				}

				.item_content {
					width: 100%;
					flex: 1;
					@include flex-center(row, flex-end, center);
					gap: 20rpx;
					font-size: 28rpx;
					color: #333;

					.inactive {
						color: #BFBFBF;
					}

					.unit {
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
					}
				}

				&.upload {
					flex-direction: column;
					gap: 20rpx;

					>view {
						width: 100%;
					}

					.item_content {
						@include flex-center(row, flex-start, center);
						gap: 20rpx;

						image {
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
						}

						.item_content_image {
							position: relative;
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
							overflow: hidden;

							image {
								height: 100%;
								width: 100%;
							}

							.item_content_image_icon {
								position: absolute;
								top: 0;
								right: 0;
								padding: 5rpx;
								border-radius: 0 10rpx 0 10rpx;
								background-color: rgba(0, 0, 0, 0.5);
							}
						}
					}
				}
			}
		}

		.form_tips {
			padding: 0 25rpx;

			.form_tips_title {
				font-weight: bold;
				font-size: 26rpx;
				color: #333333;
			}

			.form_tips_cont {
				font-weight: 500;
				font-size: 24rpx;
				color: #8B8B8B;
				line-height: 35rpx;
				margin-top: 10rpx;
			}
		}
	}

	.bottom {
		padding: 20rpx 25rpx;
		background-color: #ffffff;

		.calculate_button {
			width: 100%;
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}
}
</style>