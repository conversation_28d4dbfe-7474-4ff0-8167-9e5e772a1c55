<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="发布房源" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="form_body">
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>房源标题</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入发布的房源标题" border="none"
								fontSize="28rpx" color="#333" v-model="form.title" inputAlign="right"></u--input>
						</view>
					</view>
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房产证</text>
							<text class="subtitle">可多张</text>
						</view>
						<view class="item_content">
							<view class="item_content_image" v-for="(item, index) in form.picArr">
								<image :src="$t.getImgUrl(item)" mode="">
								</image>
								<view class="item_content_image_icon" @click="form.picArr.splice(index, 1)">
									<u-icon name="close" color="#fff" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/upload_btn.png" mode=""
								@click="chooseImage" v-if="form.picArr.length < 3">
							</image>
						</view>
					</view>
					<view class="form_item upload">
						<view class="item_label">
							<text>房屋视频</text>
							<text class="subtitle">选填</text>
						</view>
						<view class="item_content">
							<view class="item_content_image" v-for="(item, index) in form.videoArr" :key="index">
								<video :src="item" mode="aspectFit" style="width: 100%; height: 100%;">
								</video>
								<view class="item_content_image_icon" @click="form.videoArr.splice(index, 1)">
									<u-icon name="close" color="#fff" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/upload_btn.png" mode=""
								@click="chooseVideo" v-if="form.videoArr.length < 1">
							</image>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>城市</text>
						</view>
						<view class="item_content">
							<text :class="{ 'inactive': !form.city }">{{ form.city || '请选择' }}</text>
							<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>小区</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入小区名称" border="none"
								fontSize="28rpx" color="#333" v-model="form.community" inputAlign="right"></u--input>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>房型</text>
						</view>
						<view class="item_content">
							<text :class="{ 'inactive': !form.roomType }">{{ form.roomType || '请选择' }}</text>
							<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>房屋面积</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入面积" border="none"
								fontSize="28rpx" color="#333" v-model="form.area" inputAlign="right"></u--input>
							<view class="unit">m²</view>
						</view>
					</view>
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>楼层信息</text>
						</view>
						<view class="item_content" style="justify-content: space-between;">
							<view class="form_column">
								<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入" border="none"
									fontSize="28rpx" color="#333" v-model="form.currentFloor"></u--input>
								<view class="unit">层</view>
							</view>
							<view class="form_column">
								<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入" border="none"
									fontSize="28rpx" color="#333" v-model="form.totalFloor"></u--input>
								<view class="unit">总层数</view>
							</view>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>期望售价</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入期望售价" border="none"
								fontSize="28rpx" color="#333" v-model="form.expectedPrice"
								inputAlign="right"></u--input>
							<view class="unit">万</view>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>联系方式</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入您的手机号" border="none"
								fontSize="28rpx" color="#333" v-model="form.contact" inputAlign="right"></u--input>
						</view>
					</view>
				</view>
				<view class="form_body">
					<!-- 户型朝向 -->
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>户型朝向</text>
						</view>
						<view class="item_content">
							<text :class="{ 'inactive': !form.orientation }">{{ form.orientation || '东南朝向' }}</text>
							<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
						</view>
					</view>

					<!-- 建成年代 -->
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>建成年代</text>
						</view>
						<view class="item_content">
							<text :class="{ 'inactive': !form.buildYear }">{{ form.buildYear || '2022' }}</text>
							<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
						</view>
					</view>

					<!-- 装修类型 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>装修类型</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.decorationType === '豪华装' }"
									@click="selectDecoration('豪华装')">
									<text>豪华装</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.decorationType === '精装' }"
									@click="selectDecoration('精装')">
									<text>精装</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.decorationType === '简装' }"
									@click="selectDecoration('简装')">
									<text>简装</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.decorationType === '毛坯' }"
									@click="selectDecoration('毛坯')">
									<text>毛坯</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 建筑类型 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>建筑类型</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.buildingType === '塔楼' }"
									@click="selectBuildingType('塔楼')">
									<text>塔楼</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.buildingType === '板楼' }"
									@click="selectBuildingType('板楼')">
									<text>板楼</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.buildingType === '板塔结合' }"
									@click="selectBuildingType('板塔结合')">
									<text>板塔结合</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 是否有电梯 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>是否有电梯</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.hasElevator === '有' }"
									@click="selectElevator('有')">
									<text>有</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.hasElevator === '无' }"
									@click="selectElevator('无')">
									<text>无</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 房屋用途 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房屋用途</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.houseUsage === '住宅' }"
									@click="selectHouseUsage('住宅')">
									<text>住宅</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.houseUsage === '办公' }"
									@click="selectHouseUsage('办公')">
									<text>办公</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 房屋权属 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房屋权属</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.propertyRight === '商品房' }"
									@click="selectPropertyRight('商品房')">
									<text>商品房</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.propertyRight === '安置房' }"
									@click="selectPropertyRight('安置房')">
									<text>安置房</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 产权所属 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>产权所属</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.ownership === '共有' }"
									@click="selectOwnership('共有')">
									<text>共有</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.ownership === '非共有' }"
									@click="selectOwnership('非共有')">
									<text>非共有</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 房本条件 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房本条件</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.propertyCondition === '已上传' }"
									@click="selectPropertyCondition('已上传')">
									<text>已上传</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 供暖方式 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>供暖方式</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.heatingType === '中央空调' }"
									@click="selectHeatingType('中央空调')">
									<text>中央空调</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.heatingType === '有' }"
									@click="selectHeatingType('有')">
									<text>有</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.heatingType === '无' }"
									@click="selectHeatingType('无')">
									<text>无</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 车位 -->
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>车位</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.parking === '有' }"
									@click="selectParking('有')">
									<text>有</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.parking === '无' }"
									@click="selectParking('无')">
									<text>无</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 房屋标签 -->
					<view class="form_item upload">
						<view class="item_label">
							<text>房屋标签</text>
							<text class="subtitle">(可多选)</text>
						</view>
						<view class="item_content">
							<view class="tag_group">
								<view class="tag_item" :class="{ 'active': form.houseTags.includes('优质户型') }"
									@click="toggleHouseTag('优质户型')">
									<text>优质户型</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.houseTags.includes('全明格局') }"
									@click="toggleHouseTag('全明格局')">
									<text>全明格局</text>
								</view>
								<view class="tag_item" :class="{ 'active': form.houseTags.includes('南北通透') }"
									@click="toggleHouseTag('南北通透')">
									<text>南北通透</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom" class="bottom">
				<view class="calculate_button" @click="submitForm">
					确认发布
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			form: {
				title: '',
				picArr: [],
				videoArr: [],
				city: '',
				community: '',
				roomType: '',
				area: '',
				currentFloor: '',
				totalFloor: '',
				expectedPrice: '',
				contact: '',
				orientation: '',
				buildYear: '',
				decorationType: '',
				buildingType: '',
				hasElevator: '',
				houseUsage: '',
				propertyRight: '',
				ownership: '',
				propertyCondition: '',
				heatingType: '',
				parking: '',
				houseTags: []
			}
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},

		// 图片上传
		chooseImage() {
			if (this.form.picArr.length >= 3) {
				this.$t.toast("最多上传3张图片");
				return;
			}
			this.$t.chooseImgUpload().then((url) => {
				this.form.picArr.push(url);
			});
		},

		// 视频上传
		chooseVideo() {
			if (this.form.videoArr.length >= 1) {
				this.$t.toast("最多上传1个视频");
				return;
			}
			// 这里需要实现视频上传功能
			uni.chooseVideo({
				sourceType: ['camera', 'album'],
				maxDuration: 60,
				camera: 'back',
				success: (res) => {
					// 这里应该上传到服务器并获取URL
					this.form.videoArr.push(res.tempFilePath);
				}
			});
		},

		// 装修类型选择
		selectDecoration(type) {
			this.form.decorationType = type;
		},

		// 建筑类型选择
		selectBuildingType(type) {
			this.form.buildingType = type;
		},

		// 电梯选择
		selectElevator(type) {
			this.form.hasElevator = type;
		},

		// 房屋用途选择
		selectHouseUsage(type) {
			this.form.houseUsage = type;
		},

		// 房屋权属选择
		selectPropertyRight(type) {
			this.form.propertyRight = type;
		},

		// 产权所属选择
		selectOwnership(type) {
			this.form.ownership = type;
		},

		// 房本条件选择
		selectPropertyCondition(type) {
			this.form.propertyCondition = type;
		},

		// 供暖方式选择
		selectHeatingType(type) {
			this.form.heatingType = type;
		},

		// 车位选择
		selectParking(type) {
			this.form.parking = type;
		},

		// 房屋标签切换（多选）
		toggleHouseTag(tag) {
			const index = this.form.houseTags.indexOf(tag);
			if (index > -1) {
				this.form.houseTags.splice(index, 1);
			} else {
				this.form.houseTags.push(tag);
			}
		},

		// 表单验证
		validateForm() {
			if (!this.form.title) {
				this.$t.toast("请输入房源标题");
				return false;
			}
			if (this.form.picArr.length === 0) {
				this.$t.toast("请上传房产证");
				return false;
			}
			if (!this.form.city) {
				this.$t.toast("请选择城市");
				return false;
			}
			if (!this.form.community) {
				this.$t.toast("请输入小区名称");
				return false;
			}
			if (!this.form.roomType) {
				this.$t.toast("请选择房型");
				return false;
			}
			if (!this.form.area) {
				this.$t.toast("请输入房屋面积");
				return false;
			}
			if (!this.form.currentFloor) {
				this.$t.toast("请输入当前楼层");
				return false;
			}
			if (!this.form.totalFloor) {
				this.$t.toast("请输入总楼层数");
				return false;
			}
			if (!this.form.expectedPrice) {
				this.$t.toast("请输入期望售价");
				return false;
			}
			if (!this.form.contact) {
				this.$t.toast("请输入联系方式");
				return false;
			}
			return true;
		},

		// 提交表单
		submitForm() {
			if (!this.validateForm()) {
				return;
			}

			// 这里实现表单提交逻辑
			console.log("表单数据:", this.form);
			this.$t.toast("发布成功");

			// 提交成功后返回上一页
			setTimeout(() => {
				this.$t.gotoBack();
			}, 1500);
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding: 20rpx 0;
		@include flex-center(column, null, null);
		gap: 20rpx;

		.form_body {
			width: 100%;
			padding: 0 25rpx;
			background-color: #fff;



			.form_item {
				width: 100%;
				height: auto;
				@include flex-center(row, space-between, center);
				padding: 30rpx 0;
				border-bottom: 1rpx solid #eee;

				// 表单行
				.form_column {
					width: 33%;
					@include flex-center(row, space-between, center);
				}

				.item_label {
					@include flex-center(row, flex-start, center);
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;

					.required {
						color: #F63030
					}

					.subtitle {
						font-weight: 500;
						font-size: 22rpx;
						color: #BFBFBF;
						margin-left: 15rpx;
					}
				}

				.item_content {
					width: 100%;
					flex: 1;
					@include flex-center(row, flex-end, center);
					gap: 20rpx;
					font-size: 28rpx;
					color: #333;

					.inactive {
						color: #BFBFBF;
					}

					.unit {
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
					}

					.tag_group {
						width: 100%;
						@include flex-center(row, flex-end, center);
						flex-wrap: wrap;
						gap: 20rpx;

						.tag_item {
							@include flex-center(row, center center);
							width: 160rpx;
							height: 56rpx;
							border-radius: 60rpx;
							background-color: #F8F8F8;
							font-weight: 500;
							font-size: 26rpx;
							color: #333333;
							transition: all 0.3s;

							&.active {
								color: #006AFC;
								background-color: rgba(0, 106, 252, 0.1);
							}

							text {
								font-size: 26rpx;
							}
						}
					}
				}

				&.upload {
					flex-direction: column;
					gap: 20rpx;

					>view {
						width: 100%;
					}

					.item_content {
						@include flex-center(row, flex-start, center);
						gap: 20rpx;

						image {
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
						}

						.item_content_image {
							position: relative;
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
							overflow: hidden;

							image {
								height: 100%;
								width: 100%;
							}

							.item_content_image_icon {
								position: absolute;
								top: 0;
								right: 0;
								padding: 5rpx;
								border-radius: 0 10rpx 0 10rpx;
								background-color: rgba(0, 0, 0, 0.5);
							}
						}
					}
				}
			}
		}

		.form_tips {
			padding: 0 25rpx;

			.form_tips_title {
				font-weight: bold;
				font-size: 26rpx;
				color: #333333;
			}

			.form_tips_cont {
				font-weight: 500;
				font-size: 24rpx;
				color: #8B8B8B;
				line-height: 35rpx;
				margin-top: 10rpx;
			}
		}
	}

	.bottom {
		padding: 20rpx 25rpx;
		background-color: #ffffff;

		.calculate_button {
			width: 100%;
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}
}
</style>